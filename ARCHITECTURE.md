# EA 外匯交易程式架構設計文件

## 專案概述
基於 PRD 分析，本 EA 將採用物件導向設計，將功能模組化以提高可維護性和擴展性。

## 核心物件導向組件架構

### 1. 主控制器 (Main Controller)
**類別名稱**: `CEAController`
**職責**: 
- 統籌所有模組的初始化和協調
- 處理 OnTick() 事件的主要邏輯流程
- 管理 EA 的整體狀態

**主要方法**:
- `Initialize()` - 初始化所有子模組
- `OnTick()` - 主要交易邏輯處理
- `OnDeinit()` - 清理資源
- `UpdateUI()` - 更新使用者介面

### 2. 環境管理器 (Environment Manager)
**類別名稱**: `CEnvironmentManager`
**職責**:
- 檢查交易權限和網路連線
- 處理伺服器錯誤和重試機制
- 驗證交易環境的可用性

**主要方法**:
- `CheckTradingPermission()` - 檢查交易權限
- `CheckConnection()` - 檢查網路連線
- `HandleServerError(int errorCode)` - 處理伺服器錯誤
- `IsEnvironmentReady()` - 環境就緒檢查

### 3. 訊號管理器 (Signal Manager)
**類別名稱**: `CSignalManager`
**職責**:
- 計算 RSI、MACD 技術指標
- 生成買入/賣出訊號
- 管理訊號的有效性驗證

**主要方法**:
- `CalculateRSI()` - 計算 RSI 指標
- `CalculateMACD()` - 計算 MACD 指標
- `GetBuySignal()` - 檢查買入訊號
- `GetSellSignal()` - 檢查賣出訊號
- `IsSignalValid()` - 驗證訊號有效性

### 4. 趨勢過濾器 (Trend Filter)
**類別名稱**: `CTrendFilter`
**職責**:
- 使用布林通道判斷市場趨勢
- 過濾逆勢交易訊號
- 提供趨勢狀態資訊

**主要方法**:
- `CalculateBollingerBands()` - 計算布林通道
- `GetTrendDirection()` - 獲取趨勢方向
- `ShouldAllowBuy()` - 是否允許買入
- `ShouldAllowSell()` - 是否允許賣出
- `GetTrendStatus()` - 獲取趨勢狀態字串

### 5. 訂單管理器 (Order Manager)
**類別名稱**: `COrderManager`
**職責**:
- 管理所有交易訂單的開倉、平倉
- 追蹤訂單狀態和盈虧
- 處理訂單執行錯誤

**主要方法**:
- `OpenOrder(int orderType, double lots)` - 開立訂單
- `CloseOrder(int ticket)` - 平倉訂單
- `CloseAllOrders()` - 平掉所有訂單
- `GetOrderCount()` - 獲取訂單數量
- `GetTotalProfit()` - 獲取總盈虧
- `GetOrdersByMagic()` - 根據 MagicNumber 獲取訂單

### 6. 馬丁格爾策略管理器 (Martingale Manager)
**類別名稱**: `CMartingaleManager`
**職責**:
- 實施馬丁格爾加碼策略
- 計算動態加碼距離和手數
- 管理加碼邏輯

**主要方法**:
- `CalculateNextLotSize()` - 計算下一單手數
- `CalculatePipStep()` - 計算動態加碼距離
- `ShouldAddPosition()` - 是否應該加碼
- `GetNextEntryPrice()` - 獲取下一單進場價格
- `GetCurrentLevel()` - 獲取當前加碼層級

### 7. 風險管理器 (Risk Manager)
**類別名稱**: `CRiskManager`
**職責**:
- 監控帳戶回撤
- 管理追蹤止損
- 執行風控規則

**主要方法**:
- `CheckMaxDrawdown()` - 檢查最大回撤
- `UpdateTrailingStop()` - 更新追蹤止損
- `IsTrailingStopActive()` - 追蹤止損是否激活
- `CalculateDrawdownPercent()` - 計算回撤百分比
- `ShouldStopTrading()` - 是否應該停止交易

### 8. 使用者介面管理器 (UI Manager)
**類別名稱**: `CUIManager`
**職責**:
- 在圖表上顯示資訊面板
- 更新即時狀態資訊
- 管理介面元素

**主要方法**:
- `CreateInfoPanel()` - 創建資訊面板
- `UpdatePanel()` - 更新面板資訊
- `ShowStatus(string status)` - 顯示狀態資訊
- `ShowPositionInfo()` - 顯示倉位資訊
- `ShowRiskInfo()` - 顯示風險資訊

### 9. 參數管理器 (Parameter Manager)
**類別名稱**: `CParameterManager`
**職責**:
- 管理所有可配置參數
- 提供參數驗證
- 處理參數的讀取和設定

**主要方法**:
- `LoadParameters()` - 載入參數
- `ValidateParameters()` - 驗證參數有效性
- `GetParameter(string name)` - 獲取參數值
- `SetParameter(string name, double value)` - 設定參數值

### 10. 工具類 (Utility Classes)
**類別名稱**: `CATRCalculator`, `CLogger`
**職責**:
- ATR 計算工具
- 日誌記錄功能
- 其他輔助功能

## 類別關係圖概念
```
CEAController (主控制器)
├── CEnvironmentManager (環境管理)
├── CSignalManager (訊號管理)
├── CTrendFilter (趨勢過濾)
├── COrderManager (訂單管理)
├── CMartingaleManager (馬丁策略)
├── CRiskManager (風險管理)
├── CUIManager (介面管理)
├── CParameterManager (參數管理)
└── CATRCalculator, CLogger (工具類)
```

## 開發順序建議
1. **基礎架構**: 參數管理器、工具類
2. **核心功能**: 訊號管理器、趨勢過濾器
3. **交易邏輯**: 訂單管理器、馬丁格爾管理器
4. **風險控制**: 風險管理器
5. **使用者介面**: UI 管理器
6. **整合測試**: 主控制器整合所有模組

## 檔案結構規劃
```
/Include
  /EA_Core
    - CEAController.mqh
    - CEnvironmentManager.mqh
    - CSignalManager.mqh
    - CTrendFilter.mqh
    - COrderManager.mqh
    - CMartingaleManager.mqh
    - CRiskManager.mqh
    - CUIManager.mqh
    - CParameterManager.mqh
  /Utils
    - CATRCalculator.mqh
    - CLogger.mqh
    - Constants.mqh
/Experts
  - Gemini_Martingale.mq4 (主程式)
```

這個架構設計遵循單一職責原則，每個類別都有明確的功能範圍，便於後續的開發、測試和維護。
