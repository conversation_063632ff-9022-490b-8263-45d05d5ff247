EA 外匯交易程式產品需求文件 (PRD)
|

| 文件版本 | 狀態 | 作者 | 最後更新日期 |
| v1.1 | 定稿 | PRD 專家 & 使用者 | 2025年7月14日 |

1. 專案概述與目標 (Project Overview & Goals)
1.1 專案概述
本專案旨在開發一款用於 MetaTrader 4 (MT4) 平台的全自動外匯交易程式（Expert Advisor, EA）。此 EA 的核心策略將以馬丁格爾（Martingale）加碼策略為基礎，並整合多種技術指標（RSI, MACD, Bollinger Bands）進行趨勢判斷與訊號過濾，以提高交易決策的品質。

1.2 產品目標
自動化交易：實現 24/7 全自動交易，無需人工干預。

智慧決策：結合趨勢與震盪指標，避免在不利的市場條件下進行逆勢交易。

風險可控：內建靈活的風險管理機制，包括最大回撤限制、動態加碼步長與智慧追蹤止損，以保護帳戶資金。

高度客製化：提供全面的參數配置選項，讓使用者可以根據不同的交易品種和市場環境進行回測與優化。

2. 技術堆疊 (Technology Stack)
程式語言: MQL4 (MetaQuotes Language 4)

開發架構: 採用物件導向程式設計（OOP）原則，將不同功能（如訊號管理、訂單管理、風險管理）模組化，以提高程式碼的可讀性、可維護性和擴展性。

3. 開發模式 (Development Model)
開發方法: 本專案將採用靈活的個人開發模式，主要由開發者與 AI 協同助手合作完成。

團隊與里程碑: 此為業餘性質的個人專案，不設立正式的開發團隊、時間表或里程碑。開發進度將根據開發者的時間和興趣自由推進。

4. 核心功能詳述 (Features & Functionalities)
4.1 交易管理 (Trade Management)
開倉: 根據訊號管理與趨勢過濾的綜合結果，執行市場價買入（Buy）或賣出（Sell）訂單。

平倉:

使用者可手動平倉。

觸發智慧追蹤止損後自動平倉。

觸發最大回撤風控規則後，清掉所有倉位。

智慧追蹤止損 (Adaptive Trailing Stop Loss):

觸發條件: 當整個倉位組（所有馬丁單）的總體浮動盈利達到 TrailingStopActivationPips 點數後被激活。

止損距離: 止損距離根據 ATR 動態計算：止損距離 = ATR * TrailingStopAtrMultiplier。

4.2 訂單管理 (Order Management)
透過唯一的 MagicNumber 識別並管理由本 EA 開設的訂單。

實時監控所有訂單的狀態，包括手數、盈虧、開倉價格等。

4.3 環境管理 (Environment Management)
在啟動及交易前，檢查帳戶交易權限與網路連線狀態。

處理常見的伺服器錯誤，並在適當時進行等待與重試。

4.4 訊號管理 (Signal Management)
觸發邏輯: 必須同時滿足 RSI 和 MACD 的條件才會產生有效交易訊號。

買入訊號 (Buy Signal):

RSI: 指標值從 30 以下回升至 30 以上。

MACD: 快線（DIF）由下往上穿過慢線（DEM），形成「黃金交叉」。

賣出訊號 (Sell Signal):

RSI: 指標值從 70 以上回落至 70 以下。

MACD: 快線（DIF）由上往下穿過慢線（DEM），形成「死亡交叉」。

4.5 趨勢過濾 (Trend Filter)
目的: 確保交易方向與市場宏觀趨勢一致，過濾逆勢訊號。

判斷標準: 使用布林通道（Bollinger Bands, BB）的中軌，並加入緩衝區以提高穩定性。

執行邏輯:

定義多頭趨勢狀態: 當前價格 > (BB 中軌 + TrendFilterBufferPips)。

定義空頭趨勢狀態: 當前價格 < (BB 中軌 - TrendFilterBufferPips)。

交易規則:

在多頭趨勢狀態下，僅監聽並執行有效的買入訊號，忽略所有賣出訊號。

在空頭趨勢狀態下，僅監聽並執行有效的賣出訊號，忽略所有買入訊號。

5. 馬丁策略 (Martingale Strategy)
當訂單虧損時，在動態計算的點數距離後，以倍增的交易量開立同向的新訂單。

動態加碼距離 (PipStep): 加碼距離將根據市場波動性（ATR）動態調整，公式為：PipStep = ATR * ATR_Multiplier_PipStep。

6. 風險管理 (Risk Management)
最大回撤 (Maximum Drawdown): 監控帳戶的總體浮動虧損。當浮虧佔帳戶餘額的百分比達到 MaxDrawdownPercent 時，EA 將自動平掉所有倉位，並在當天停止所有新的交易。

7. 使用者介面 (User Interface)
在圖表左上角顯示一個資訊面板，實時呈現 EA 核心狀態。

面板顯示內容:

總體狀態: EA 名稱、運行狀態、當前趨勢狀態 (多頭/空頭/等待)。

倉位資訊: 持倉單數、總交易手數、總體浮動盈虧 (點數/貨幣)。

馬丁策略資訊: 下一單預計手數、下一單預計開倉價格。

風險管理: 風控回撤設定值、當前回撤水平、追蹤止損狀態 (激活/未激活)。

8. 可配置參數總覽 (Summary of Configurable Parameters)
| 參數類別 | 參數名稱 | 建議預設值 | 說明 |
| 交易量管理 | InitialLot | 0.01 | 起始交易手數。 |
|  | Multiplier | 2.0 | 馬丁策略的交易量倍數。 |
|  | MaxTrades | 7 | 允許的最大加碼次數。 |
| 馬丁策略 | ATR_Period_For_PipStep | 14 | 用於計算加碼距離的 ATR 週期。 |
|  | ATR_Multiplier_PipStep | 1.5 | 計算加碼距離的 ATR 倍數。距離 = ATR * 此倍數 |
| 風險管理 | MaxDrawdownPercent | 20.0 | 帳戶最大回撤百分比，觸發後清倉停機。 |
|  | TrailingStopActivationPips | 500 | 激活追蹤止損所需的總盈利點數。 |
|  | TrailingStopAtrMultiplier | 2.5 | 計算動態追蹤止損距離的 ATR 倍數。 |
| 訊號與過濾 | RSI_Period | 14 | RSI 指標的計算週期。 |
|  | MACD_Fast_EMA | 12 | MACD 指標的快線週期。 |
|  | MACD_Slow_EMA | 26 | MACD 指標的慢線週期。 |
|  | MACD_Signal_SMA | 9 | MACD 指標的訊號線週期。 |
|  | BB_Period | 20 | 布林通道指標的計算週期。 |
|  | BB_Deviation | 2.0 | 布林通道指標的標準差。 |
|  | TrendFilterBufferPips | 10 | 趨勢過濾的價格緩衝區點數。 |
| EA 識別 | MagicNumber | 12345 | EA 訂單的唯一識別碼。 |

9. 未來計劃 (Future Plans)
日誌與通知 (Logging & Notifications): 增加詳細的操作日誌記錄功能，並在關鍵事件（如開倉、風控觸發）發生時，提供手機推送通知選項。

錯誤處理 (Advanced Error Handling): 建立更完善的錯誤處理機制，以應對各種交易伺服器返回的錯誤碼。