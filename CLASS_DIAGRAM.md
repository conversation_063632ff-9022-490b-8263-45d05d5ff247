# 類別關係圖

## 簡化的類別關係圖

```
┌─────────────────────────────────────────────────────────────────┐
│                        CEAController                            │
│                      (主控制器)                                  │
├─────────────────────────────────────────────────────────────────┤
│ - m_environmentManager: CEnvironmentManager*                   │
│ - m_signalManager: CSignalManager*                             │
│ - m_trendFilter: CTrendFilter*                                 │
│ - m_orderManager: COrderManager*                               │
│ - m_martingaleManager: CMartingaleManager*                     │
│ - m_riskManager: CRiskManager*                                 │
│ - m_uiManager: CUIManager*                                     │
│ - m_paramManager: CParameterManager*                           │
├─────────────────────────────────────────────────────────────────┤
│ + Initialize(): bool                                            │
│ + OnTick(): void                                               │
│ + OnDeinit(): void                                             │
│ + ProcessTradingLogic(): void                                  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                │ 管理所有子模組
                                ▼
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│                 │                 │                 │                 │
▼                 ▼                 ▼                 ▼                 ▼

┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│CEnvironmentMgr  │ │  CSignalManager │ │  CTrendFilter   │ │  COrderManager  │
│   (環境管理)     │ │   (訊號管理)     │ │   (趨勢過濾)     │ │   (訂單管理)     │
├─────────────────┤ ├─────────────────┤ ├─────────────────┤ ├─────────────────┤
│+ CheckTrading() │ │+ GetBuySignal() │ │+ GetTrend()     │ │+ OpenOrder()    │
│+ CheckConnect() │ │+ GetSellSignal()│ │+ ShouldAllow()  │ │+ CloseOrder()   │
│+ HandleError()  │ │+ UpdateIndic()  │ │+ UpdateBB()     │ │+ GetProfit()    │
└─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────────┘

┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│CMartingaleMgr   │ │  CRiskManager   │ │   CUIManager    │ │CParameterMgr    │
│  (馬丁策略)      │ │   (風險管理)     │ │   (介面管理)     │ │  (參數管理)      │
├─────────────────┤ ├─────────────────┤ ├─────────────────┤ ├─────────────────┤
│+ CalcNextLot()  │ │+ CheckDrawdown()│ │+ CreatePanel()  │ │+ LoadParams()   │
│+ CalcPipStep()  │ │+ UpdateTrail()  │ │+ UpdatePanel()  │ │+ ValidateParams()│
│+ ShouldAdd()    │ │+ IsTrailActive()│ │+ ShowStatus()   │ │+ GetParameter() │
└─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────────┘

                    ┌─────────────────┐ ┌─────────────────┐
                    │ CATRCalculator  │ │    CLogger      │
                    │   (ATR計算)     │ │   (日誌記錄)     │
                    ├─────────────────┤ ├─────────────────┤
                    │+ CalculateATR() │ │+ LogInfo()      │
                    │+ GetCurrentATR()│ │+ LogError()     │
                    │+ UpdateATR()    │ │+ LogTrade()     │
                    └─────────────────┘ └─────────────────┘
```

## 詳細的互動關係

### 1. 主控制器與子模組關係
```
CEAController
├── 初始化階段
│   ├── CParameterManager.LoadParameters()
│   ├── CEnvironmentManager.Initialize()
│   ├── CSignalManager.Initialize()
│   ├── CTrendFilter.Initialize()
│   ├── COrderManager.Initialize()
│   ├── CMartingaleManager.Initialize()
│   ├── CRiskManager.Initialize()
│   └── CUIManager.Initialize()
│
└── OnTick() 執行流程
    ├── CEnvironmentManager.IsEnvironmentReady()
    ├── CSignalManager.UpdateIndicators()
    ├── CTrendFilter.UpdateBollingerBands()
    ├── CRiskManager.CheckMaxDrawdown()
    ├── 交易決策邏輯
    │   ├── CSignalManager.GetBuySignal()/GetSellSignal()
    │   ├── CTrendFilter.ShouldAllowBuy()/ShouldAllowSell()
    │   ├── CMartingaleManager.ShouldAddPosition()
    │   └── COrderManager.OpenOrder()
    ├── CRiskManager.UpdateTrailingStop()
    └── CUIManager.UpdatePanel()
```

### 2. 訊號系統互動
```
CSignalManager ←→ CTrendFilter
│                      │
│ 1. 產生原始訊號        │ 2. 趨勢過濾
│                      │
▼                      ▼
買入/賣出訊號 ────→ 趨勢確認 ────→ 最終交易決策
```

### 3. 交易執行流程
```
交易訊號確認
    │
    ▼
CMartingaleManager.CalculateNextLotSize()
    │
    ▼
COrderManager.OpenOrder()
    │
    ▼
CRiskManager.UpdateTrailingStop()
    │
    ▼
CUIManager.UpdatePanel()
```

### 4. 風險管理互動
```
CRiskManager
├── 監控 COrderManager.GetTotalProfit()
├── 計算回撤百分比
├── 檢查追蹤止損條件
└── 必要時觸發 COrderManager.CloseAllOrders()
```

## 資料流向圖

```
市場資料 (Tick)
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                    CEAController                            │
│                   (資料分發中心)                             │
└─────────────────────────────────────────────────────────────┘
    │
    ├─────────────────────────────────────────────────────────┐
    │                                                         │
    ▼                                                         ▼
┌─────────────────┐                                   ┌─────────────────┐
│  CSignalManager │ ──── RSI/MACD 訊號 ────────────→ │  CTrendFilter   │
│                 │                                   │                 │
│ • RSI 計算      │ ←──── 趨勢狀態 ──────────────── │ • BB 計算       │
│ • MACD 計算     │                                   │ • 趨勢判斷      │
│ • 訊號生成      │                                   │ • 訊號過濾      │
└─────────────────┘                                   └─────────────────┘
    │                                                         │
    │                     確認的交易訊號                        │
    └─────────────────────────┬─────────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ CMartingaleMgr  │
                    │                 │
                    │ • 計算手數      │
                    │ • 計算加碼距離   │
                    │ • 判斷加碼條件   │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  COrderManager  │
                    │                 │
                    │ • 執行交易      │
                    │ • 管理訂單      │
                    │ • 計算盈虧      │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  CRiskManager   │
                    │                 │
                    │ • 監控回撤      │
                    │ • 追蹤止損      │
                    │ • 風險控制      │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   CUIManager    │
                    │                 │
                    │ • 更新介面      │
                    │ • 顯示狀態      │
                    │ • 即時資訊      │
                    └─────────────────┘
```

## 依賴關係說明

### 強依賴 (Strong Dependencies)
- `CEAController` → 所有子模組 (組合關係)
- `CMartingaleManager` → `CATRCalculator` (使用 ATR 計算)
- `CRiskManager` → `CATRCalculator` (追蹤止損計算)

### 弱依賴 (Weak Dependencies)
- `CSignalManager` ↔ `CTrendFilter` (資料交換)
- 所有模組 → `CLogger` (日誌記錄)
- 所有模組 → `CParameterManager` (參數讀取)

### 介面依賴 (Interface Dependencies)
- `CUIManager` → 所有模組 (讀取狀態資訊)
- `CRiskManager` → `COrderManager` (監控和控制)

這個設計確保了模組間的低耦合和高內聚，便於維護和擴展。
