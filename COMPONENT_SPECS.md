# 組件詳細規格文件

## 1. CEAController (主控制器)

### 屬性 (Private Members)
```cpp
CEnvironmentManager* m_environmentManager;
CSignalManager* m_signalManager;
CTrendFilter* m_trendFilter;
COrderManager* m_orderManager;
CMartingaleManager* m_martingaleManager;
CRiskManager* m_riskManager;
CUIManager* m_uiManager;
CParameterManager* m_paramManager;
bool m_isInitialized;
bool m_tradingEnabled;
```

### 核心方法規格
- `bool Initialize()`: 初始化所有子模組，返回成功/失敗狀態
- `void OnTick()`: 主要交易邏輯，按順序執行環境檢查→訊號檢測→風險評估→交易執行
- `void OnDeinit()`: 清理所有資源，保存狀態
- `void ProcessTradingLogic()`: 核心交易決策流程

## 2. CSignalManager (訊號管理器)

### 屬性
```cpp
double m_rsiCurrent, m_rsiPrevious;
double m_macdMain[], m_macdSignal[];
int m_rsiPeriod;
int m_macdFast, m_macdSlow, m_macdSignal;
bool m_lastBuySignal, m_lastSellSignal;
```

### 核心方法規格
- `bool GetBuySignal()`: 檢查 RSI 從 30 以下回升 + MACD 黃金交叉
- `bool GetSellSignal()`: 檢查 RSI 從 70 以上回落 + MACD 死亡交叉
- `void UpdateIndicators()`: 更新所有技術指標數值
- `bool IsRSIBuyCondition()`: RSI 買入條件檢查
- `bool IsRSISellCondition()`: RSI 賣出條件檢查
- `bool IsMACD_BullishCross()`: MACD 黃金交叉檢查
- `bool IsMACD_BearishCross()`: MACD 死亡交叉檢查

## 3. CTrendFilter (趨勢過濾器)

### 屬性
```cpp
double m_bbUpper, m_bbMiddle, m_bbLower;
int m_bbPeriod;
double m_bbDeviation;
double m_trendBufferPips;
int m_currentTrend; // 1=多頭, -1=空頭, 0=震盪
```

### 核心方法規格
- `int GetTrendDirection()`: 返回當前趨勢方向 (1/-1/0)
- `bool ShouldAllowBuy()`: 多頭趨勢下允許買入
- `bool ShouldAllowSell()`: 空頭趨勢下允許賣出
- `void UpdateBollingerBands()`: 更新布林通道數值
- `string GetTrendStatusText()`: 返回趨勢狀態文字

## 4. COrderManager (訂單管理器)

### 屬性
```cpp
int m_magicNumber;
int m_orderTickets[];
double m_totalProfit;
int m_totalOrders;
```

### 核心方法規格
- `int OpenOrder(int orderType, double lots, double price)`: 開立訂單，返回 ticket
- `bool CloseOrder(int ticket)`: 平倉指定訂單
- `bool CloseAllOrders()`: 平掉所有 EA 訂單
- `void UpdateOrderInfo()`: 更新訂單資訊
- `double GetTotalProfit()`: 計算總盈虧
- `double GetTotalLots()`: 計算總手數
- `int GetOrderCount()`: 獲取訂單數量
- `double GetLastOrderPrice()`: 獲取最後一單價格

## 5. CMartingaleManager (馬丁格爾管理器)

### 屬性
```cpp
double m_initialLot;
double m_multiplier;
int m_maxTrades;
int m_currentLevel;
double m_atrPeriod;
double m_atrMultiplier;
```

### 核心方法規格
- `double CalculateNextLotSize()`: 根據當前層級計算下一單手數
- `double CalculatePipStep()`: 使用 ATR 計算動態加碼距離
- `bool ShouldAddPosition(double currentPrice)`: 判斷是否應該加碼
- `double GetNextEntryPrice(int orderType)`: 計算下一單進場價格
- `int GetCurrentLevel()`: 獲取當前馬丁層級
- `bool CanAddMorePositions()`: 是否還能繼續加碼

## 6. CRiskManager (風險管理器)

### 屬性
```cpp
double m_maxDrawdownPercent;
double m_currentDrawdownPercent;
double m_trailingStopActivationPips;
double m_trailingStopAtrMultiplier;
bool m_isTrailingStopActive;
double m_trailingStopLevel;
bool m_tradingHalted;
```

### 核心方法規格
- `bool CheckMaxDrawdown()`: 檢查是否觸發最大回撤
- `void UpdateTrailingStop()`: 更新追蹤止損水平
- `bool ShouldActivateTrailingStop()`: 是否應該激活追蹤止損
- `bool ShouldCloseByTrailingStop()`: 是否應該追蹤止損平倉
- `double CalculateDrawdownPercent()`: 計算當前回撤百分比
- `void HaltTrading()`: 停止交易功能
- `bool IsTradingAllowed()`: 是否允許交易

## 7. CUIManager (介面管理器)

### 屬性
```cpp
string m_panelName;
int m_panelX, m_panelY;
color m_textColor;
int m_fontSize;
```

### 核心方法規格
- `void CreateInfoPanel()`: 創建資訊面板物件
- `void UpdatePanel()`: 更新所有面板資訊
- `void ShowEAStatus(string status)`: 顯示 EA 狀態
- `void ShowPositionInfo(int orders, double lots, double profit)`: 顯示倉位資訊
- `void ShowMartingaleInfo(double nextLot, double nextPrice)`: 顯示馬丁資訊
- `void ShowRiskInfo(double drawdown, bool trailingActive)`: 顯示風險資訊
- `void ShowTrendInfo(string trend)`: 顯示趨勢資訊

## 8. CParameterManager (參數管理器)

### 屬性
```cpp
// 交易參數
double m_initialLot;
double m_multiplier;
int m_maxTrades;
// 技術指標參數
int m_rsiPeriod;
int m_macdFast, m_macdSlow, m_macdSignalPeriod;
int m_bbPeriod;
double m_bbDeviation;
// 風險管理參數
double m_maxDrawdownPercent;
double m_trailingStopActivationPips;
double m_trailingStopAtrMultiplier;
// 其他參數
int m_magicNumber;
double m_trendFilterBufferPips;
```

### 核心方法規格
- `bool LoadParameters()`: 從 EA 輸入參數載入設定
- `bool ValidateParameters()`: 驗證參數合理性
- `double GetDouble(string paramName)`: 獲取浮點參數
- `int GetInt(string paramName)`: 獲取整數參數
- `void SetParameter(string name, double value)`: 設定參數值

## 9. 工具類規格

### CATRCalculator
```cpp
class CATRCalculator {
private:
    int m_period;
    double m_atrBuffer[];
public:
    double CalculateATR(int period, int shift = 0);
    double GetCurrentATR();
    void UpdateATR();
};
```

### CLogger
```cpp
class CLogger {
private:
    string m_logFile;
    bool m_enableLogging;
public:
    void LogInfo(string message);
    void LogError(string message);
    void LogTrade(string action, double lots, double price);
    void SetLogLevel(int level);
};
```

## 資料流程
1. **初始化階段**: CEAController → 各子模組初始化
2. **每次 Tick**: 環境檢查 → 指標更新 → 訊號檢測 → 趨勢過濾 → 交易決策 → 風險檢查 → UI 更新
3. **交易執行**: 訊號確認 → 馬丁邏輯 → 訂單管理 → 風險監控
4. **風控觸發**: 回撤檢查 → 追蹤止損 → 強制平倉 → 停止交易

這個規格為每個組件提供了清晰的介面定義，便於後續的實作和測試。
